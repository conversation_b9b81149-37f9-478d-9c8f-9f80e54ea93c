/**
 * Performance Monitor and <PERSON><PERSON>r Handler
 * Enhanced monitoring for the Apple Style Website
 */

// Performance monitoring
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            loadTime: 0,
            renderTime: 0,
            interactionTime: 0,
            errors: []
        };
        this.init();
    }

    init() {
        // Monitor page load performance
        window.addEventListener('load', () => {
            this.measureLoadTime();
            this.measureRenderTime();
        });

        // Monitor user interactions
        this.monitorInteractions();

        // Monitor errors
        this.monitorErrors();

        // Monitor memory usage (if available)
        this.monitorMemory();
    }

    measureLoadTime() {
        if ('performance' in window) {
            const navigation = performance.getEntriesByType('navigation')[0];
            if (navigation) {
                this.metrics.loadTime = navigation.loadEventEnd - navigation.fetchStart;
                console.log(`Page load time: ${this.metrics.loadTime.toFixed(2)}ms`);
            }
        }
    }

    measureRenderTime() {
        if ('performance' in window) {
            const paintEntries = performance.getEntriesByType('paint');
            const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint');
            if (fcp) {
                this.metrics.renderTime = fcp.startTime;
                console.log(`First Contentful Paint: ${this.metrics.renderTime.toFixed(2)}ms`);
            }
        }
    }

    monitorInteractions() {
        const interactionEvents = ['click', 'keydown', 'touchstart'];
        
        interactionEvents.forEach(eventType => {
            document.addEventListener(eventType, (e) => {
                const startTime = performance.now();
                
                // Use requestAnimationFrame to measure interaction response time
                requestAnimationFrame(() => {
                    const endTime = performance.now();
                    const interactionTime = endTime - startTime;
                    
                    if (interactionTime > 100) { // Log slow interactions
                        console.warn(`Slow interaction detected: ${interactionTime.toFixed(2)}ms for ${eventType} on`, e.target);
                    }
                });
            }, { passive: true });
        });
    }

    monitorErrors() {
        // JavaScript errors
        window.addEventListener('error', (event) => {
            const error = {
                type: 'javascript',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                timestamp: new Date().toISOString()
            };
            
            this.metrics.errors.push(error);
            this.reportError(error);
        });

        // Promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            const error = {
                type: 'promise',
                message: event.reason?.message || 'Unhandled promise rejection',
                timestamp: new Date().toISOString()
            };
            
            this.metrics.errors.push(error);
            this.reportError(error);
        });

        // Resource loading errors
        window.addEventListener('error', (event) => {
            if (event.target !== window) {
                const error = {
                    type: 'resource',
                    message: `Failed to load ${event.target.tagName}: ${event.target.src || event.target.href}`,
                    timestamp: new Date().toISOString()
                };
                
                this.metrics.errors.push(error);
                this.reportError(error);
            }
        }, true);
    }

    monitorMemory() {
        if ('memory' in performance) {
            setInterval(() => {
                const memory = performance.memory;
                const memoryUsage = {
                    used: Math.round(memory.usedJSHeapSize / 1048576), // MB
                    total: Math.round(memory.totalJSHeapSize / 1048576), // MB
                    limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
                };

                // Warn if memory usage is high
                if (memoryUsage.used > memoryUsage.limit * 0.8) {
                    console.warn('High memory usage detected:', memoryUsage);
                }
            }, 30000); // Check every 30 seconds
        }
    }

    reportError(error) {
        console.error('Error reported:', error);
        
        // In a real application, you would send this to your error tracking service
        // Example: sendToErrorTrackingService(error);
    }

    getMetrics() {
        return this.metrics;
    }
}

// Image lazy loading optimization
class LazyImageLoader {
    constructor() {
        this.images = [];
        this.imageObserver = null;
        this.init();
    }

    init() {
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.imageObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px'
            });

            this.observeImages();
        } else {
            // Fallback for browsers without IntersectionObserver
            this.loadAllImages();
        }
    }

    observeImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            this.imageObserver.observe(img);
        });
    }

    loadImage(img) {
        if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    }

    loadAllImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => this.loadImage(img));
    }
}

// Service Worker registration for caching
class ServiceWorkerManager {
    constructor() {
        this.init();
    }

    init() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                this.registerServiceWorker();
            });
        }
    }

    async registerServiceWorker() {
        try {
            const registration = await navigator.serviceWorker.register('/sw.js');
            console.log('Service Worker registered successfully:', registration);
        } catch (error) {
            console.log('Service Worker registration failed:', error);
        }
    }
}

// Initialize performance monitoring
document.addEventListener('DOMContentLoaded', () => {
    try {
        // Initialize performance monitor
        window.performanceMonitor = new PerformanceMonitor();
        
        // Initialize lazy loading
        window.lazyImageLoader = new LazyImageLoader();
        
        // Initialize service worker
        window.serviceWorkerManager = new ServiceWorkerManager();
        
        console.log('Performance monitoring initialized');
    } catch (error) {
        console.error('Failed to initialize performance monitoring:', error);
    }
});

// Export for debugging
window.getPerformanceMetrics = () => {
    return window.performanceMonitor?.getMetrics() || {};
};
