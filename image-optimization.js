/**
 * Image Optimization System
 * WebP support, lazy loading, and responsive images
 */

class ImageOptimization {
    constructor() {
        this.supportsWebP = false;
        this.supportsAvif = false;
        this.intersectionObserver = null;
        this.loadedImages = new Set();
        
        this.init();
    }

    async init() {
        await this.detectFormatSupport();
        this.setupLazyLoading();
        this.optimizeExistingImages();
        this.createImagePlaceholders();
        
        console.log('Image optimization system initialized');
        console.log('WebP support:', this.supportsWebP);
        console.log('AVIF support:', this.supportsAvif);
    }

    async detectFormatSupport() {
        // Test WebP support
        this.supportsWebP = await this.testImageFormat('data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA');
        
        // Test AVIF support
        this.supportsAvif = await this.testImageFormat('data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=');
    }

    testImageFormat(dataUrl) {
        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => resolve(true);
            img.onerror = () => resolve(false);
            img.src = dataUrl;
        });
    }

    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            this.intersectionObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        this.intersectionObserver.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all images with data-src
            this.observeImages();
        } else {
            // Fallback for browsers without IntersectionObserver
            this.loadAllImages();
        }
    }

    observeImages() {
        const images = document.querySelectorAll('img[data-src], [data-bg-src]');
        images.forEach(img => {
            this.intersectionObserver.observe(img);
        });
    }

    loadImage(element) {
        if (this.loadedImages.has(element)) return;

        const isBackgroundImage = element.hasAttribute('data-bg-src');
        
        if (isBackgroundImage) {
            this.loadBackgroundImage(element);
        } else {
            this.loadRegularImage(element);
        }

        this.loadedImages.add(element);
    }

    loadRegularImage(img) {
        const src = img.dataset.src;
        const srcset = img.dataset.srcset;
        const sizes = img.dataset.sizes;

        if (!src) return;

        // Create optimized source URLs
        const optimizedSrc = this.getOptimizedImageUrl(src);
        const optimizedSrcset = srcset ? this.getOptimizedSrcset(srcset) : null;

        // Show loading state
        img.classList.add('loading');

        // Create a new image to preload
        const newImg = new Image();
        
        newImg.onload = () => {
            img.src = optimizedSrc;
            if (optimizedSrcset) img.srcset = optimizedSrcset;
            if (sizes) img.sizes = sizes;
            
            img.classList.remove('loading');
            img.classList.add('loaded');
            
            // Remove data attributes
            delete img.dataset.src;
            delete img.dataset.srcset;
            delete img.dataset.sizes;

            // Trigger load event for any listeners
            img.dispatchEvent(new Event('imageLoaded'));
        };

        newImg.onerror = () => {
            img.classList.remove('loading');
            img.classList.add('error');
            console.warn('Failed to load image:', src);
        };

        newImg.src = optimizedSrc;
    }

    loadBackgroundImage(element) {
        const src = element.dataset.bgSrc;
        if (!src) return;

        const optimizedSrc = this.getOptimizedImageUrl(src);
        
        element.classList.add('loading');

        const img = new Image();
        img.onload = () => {
            element.style.backgroundImage = `url(${optimizedSrc})`;
            element.classList.remove('loading');
            element.classList.add('loaded');
            delete element.dataset.bgSrc;
        };

        img.onerror = () => {
            element.classList.remove('loading');
            element.classList.add('error');
            console.warn('Failed to load background image:', src);
        };

        img.src = optimizedSrc;
    }

    getOptimizedImageUrl(src) {
        // In a real application, you would have a CDN or image service
        // that can serve optimized formats. For now, we'll simulate this.
        
        if (this.supportsAvif && src.includes('.jpg') || src.includes('.png')) {
            // Return AVIF version if available
            return src.replace(/\.(jpg|jpeg|png)$/i, '.avif');
        } else if (this.supportsWebP && (src.includes('.jpg') || src.includes('.png'))) {
            // Return WebP version if available
            return src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        }
        
        return src;
    }

    getOptimizedSrcset(srcset) {
        // Optimize each URL in the srcset
        return srcset.split(',').map(src => {
            const [url, descriptor] = src.trim().split(' ');
            const optimizedUrl = this.getOptimizedImageUrl(url);
            return descriptor ? `${optimizedUrl} ${descriptor}` : optimizedUrl;
        }).join(', ');
    }

    optimizeExistingImages() {
        // Optimize images that are already loaded
        const images = document.querySelectorAll('img:not([data-src])');
        
        images.forEach(img => {
            if (img.complete && img.naturalHeight !== 0) {
                this.addImageEffects(img);
            } else {
                img.addEventListener('load', () => {
                    this.addImageEffects(img);
                });
            }
        });
    }

    addImageEffects(img) {
        // Add fade-in effect
        img.style.opacity = '0';
        img.style.transition = 'opacity 0.3s ease';
        
        requestAnimationFrame(() => {
            img.style.opacity = '1';
        });

        // Add error handling
        img.addEventListener('error', () => {
            this.handleImageError(img);
        });
    }

    handleImageError(img) {
        // Create fallback placeholder
        const placeholder = this.createErrorPlaceholder();
        img.parentNode.insertBefore(placeholder, img);
        img.style.display = 'none';
    }

    createErrorPlaceholder() {
        const placeholder = document.createElement('div');
        placeholder.className = 'image-error-placeholder';
        placeholder.innerHTML = `
            <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
            </svg>
            <span>图片加载失败</span>
        `;
        return placeholder;
    }

    createImagePlaceholders() {
        // Create loading placeholders for images
        const style = document.createElement('style');
        style.textContent = `
            .image-placeholder {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading-shimmer 1.5s infinite;
                border-radius: 8px;
            }

            @keyframes loading-shimmer {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            img.loading {
                opacity: 0.5;
                filter: blur(2px);
            }

            img.loaded {
                opacity: 1;
                filter: none;
                transition: all 0.3s ease;
            }

            img.error {
                opacity: 0.3;
            }

            .image-error-placeholder {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background: #f5f5f5;
                color: #999;
                padding: 20px;
                border-radius: 8px;
                min-height: 200px;
            }

            .image-error-placeholder svg {
                margin-bottom: 10px;
                opacity: 0.5;
            }

            [data-bg-src].loading {
                background-image: none !important;
                background-color: #f0f0f0;
                position: relative;
            }

            [data-bg-src].loading::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading-shimmer 1.5s infinite;
            }

            [data-bg-src].loaded::before {
                display: none;
            }

            /* Dark theme adjustments */
            [data-theme="dark"] .image-placeholder,
            [data-theme="dark"] [data-bg-src].loading {
                background: linear-gradient(90deg, #2c2c2e 25%, #3a3a3c 50%, #2c2c2e 75%);
                background-size: 200% 100%;
            }

            [data-theme="dark"] .image-error-placeholder {
                background: #2c2c2e;
                color: #8e8e93;
            }
        `;
        document.head.appendChild(style);
    }

    loadAllImages() {
        // Fallback: load all images immediately
        const images = document.querySelectorAll('img[data-src], [data-bg-src]');
        images.forEach(img => this.loadImage(img));
    }

    // Progressive image loading for better UX
    createProgressiveImage(container, src, alt = '') {
        const img = document.createElement('img');
        img.alt = alt;
        img.className = 'progressive-image';
        
        // Create low-quality placeholder
        const placeholder = this.createImagePlaceholder(container.offsetWidth, container.offsetHeight);
        container.appendChild(placeholder);
        
        // Load actual image
        img.onload = () => {
            container.removeChild(placeholder);
            container.appendChild(img);
            img.classList.add('loaded');
        };
        
        img.src = this.getOptimizedImageUrl(src);
        
        return img;
    }

    createImagePlaceholder(width, height) {
        const placeholder = document.createElement('div');
        placeholder.className = 'image-placeholder';
        placeholder.style.width = width + 'px';
        placeholder.style.height = height + 'px';
        return placeholder;
    }

    // Preload critical images
    preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('[data-critical="true"]');
        criticalImages.forEach(img => {
            if (img.dataset.src) {
                this.loadImage(img);
            }
        });
    }

    // Public API
    loadImageNow(element) {
        this.loadImage(element);
    }

    refreshObserver() {
        if (this.intersectionObserver) {
            this.observeImages();
        }
    }

    getStats() {
        return {
            supportsWebP: this.supportsWebP,
            supportsAvif: this.supportsAvif,
            loadedImages: this.loadedImages.size
        };
    }
}

// Initialize image optimization
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.imageOptimization = new ImageOptimization();
        console.log('Image optimization system loaded');
    } catch (error) {
        console.error('Failed to initialize image optimization:', error);
    }
});

// Export for debugging
window.ImageOptimization = ImageOptimization;
