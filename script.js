/**
 * Apple Style Website - Enhanced JavaScript
 * Optimized for performance, accessibility, and error handling
 */

// Global configuration
const CONFIG = {
    ANIMATION_DURATION: 300,
    SCROLL_THRESHOLD: 100,
    PARALLAX_SPEED: 0.5,
    LOADING_DELAY: 2000,
    DEBOUNCE_DELAY: 16, // ~60fps
};

// Utility functions
const utils = {
    // Debounce function for performance optimization
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Throttle function for scroll events
    throttle(func, limit) {
        let inThrottle;
        return function(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // Check if element is in viewport
    isInViewport(element, threshold = 0.1) {
        if (!element) return false;
        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight || document.documentElement.clientHeight;
        const windowWidth = window.innerWidth || document.documentElement.clientWidth;

        return (
            rect.top <= windowHeight * (1 - threshold) &&
            rect.bottom >= windowHeight * threshold &&
            rect.left <= windowWidth &&
            rect.right >= 0
        );
    },

    // Safe element selector
    $(selector) {
        try {
            return document.querySelector(selector);
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return null;
        }
    },

    // Safe element selector all
    $$(selector) {
        try {
            return document.querySelectorAll(selector);
        } catch (error) {
            console.warn(`Invalid selector: ${selector}`, error);
            return [];
        }
    }
};

// Error handling
window.addEventListener('error', (event) => {
    console.error('JavaScript Error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled Promise Rejection:', event.reason);
});

// Main initialization
document.addEventListener('DOMContentLoaded', function() {
    try {
        // Initialize all components
        initLoadingScreen();
        initScrollAnimations();
        initNavbarEffects();
        initParallaxEffects();
        initProductCardAnimations();
        initSmoothScrolling();
        initButtonEffects();
        initAccessibility();

        // Performance optimization
        requestAnimationFrame(updateAnimations);

        console.log('Website initialized successfully');
    } catch (error) {
        console.error('Initialization error:', error);
    }
});

// Loading screen management
function initLoadingScreen() {
    const loadingScreen = utils.$('.loading-screen');
    if (!loadingScreen) return;

    // Hide loading screen after content is loaded
    const hideLoading = () => {
        try {
            loadingScreen.classList.add('hidden');
            document.body.classList.add('loaded');

            // Remove loading screen from DOM after animation
            setTimeout(() => {
                if (loadingScreen.parentNode) {
                    loadingScreen.remove();
                }
            }, 500);
        } catch (error) {
            console.error('Error hiding loading screen:', error);
        }
    };

    // Wait for all critical resources to load
    if (document.readyState === 'complete') {
        setTimeout(hideLoading, CONFIG.LOADING_DELAY);
    } else {
        window.addEventListener('load', () => {
            setTimeout(hideLoading, CONFIG.LOADING_DELAY);
        });
    }
}

// Scroll-triggered animations with enhanced error handling
function initScrollAnimations() {
    if (!('IntersectionObserver' in window)) {
        console.warn('IntersectionObserver not supported');
        return;
    }

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            try {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                    // Unobserve after animation to improve performance
                    observer.unobserve(entry.target);
                }
            } catch (error) {
                console.error('Animation error:', error);
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = utils.$$('.product-card, .feature-item, .section-title');
    animateElements.forEach(el => {
        if (el) {
            el.classList.add('animate-on-scroll');
            observer.observe(el);
        }
    });

    // Store observer for cleanup
    window.scrollObserver = observer;
}

// Navbar scroll effects with enhanced error handling
function initNavbarEffects() {
    const navbar = utils.$('.navbar');
    if (!navbar) {
        console.warn('Navbar not found');
        return;
    }

    let lastScrollY = window.scrollY;
    let ticking = false;

    function updateNavbar() {
        try {
            const scrollY = window.scrollY;

            if (scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Hide/show navbar on scroll
            if (scrollY > lastScrollY && scrollY > 200) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }

            lastScrollY = scrollY;
        } catch (error) {
            console.error('Navbar update error:', error);
        } finally {
            ticking = false;
        }
    }

    const throttledUpdate = utils.throttle(updateNavbar, CONFIG.DEBOUNCE_DELAY);
    window.addEventListener('scroll', throttledUpdate, { passive: true });
}

// Parallax effects
function initParallaxEffects() {
    const parallaxElements = document.querySelectorAll('.floating-device, .performance-chart, .design-showcase');
    
    function updateParallax() {
        const scrolled = window.pageYOffset;
        const rate = scrolled * -0.5;

        parallaxElements.forEach((element, index) => {
            const speed = (index + 1) * 0.2;
            element.style.transform = `translateY(${rate * speed}px)`;
        });
    }

    let ticking = false;
    window.addEventListener('scroll', () => {
        if (!ticking) {
            requestAnimationFrame(updateParallax);
            ticking = true;
        }
    });
}

// Product card hover animations
function initProductCardAnimations() {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        const placeholder = card.querySelector('.product-placeholder');
        
        card.addEventListener('mouseenter', () => {
            // Add magnetic effect
            card.style.transform = 'translateY(-10px) scale(1.02)';
            placeholder.style.transform = 'scale(1.1) rotate(5deg)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
            placeholder.style.transform = 'scale(1) rotate(0deg)';
        });

        // Mouse move effect
        card.addEventListener('mousemove', (e) => {
            const rect = card.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;
            
            card.style.transform = `translateY(-10px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
        });
    });
}

// Smooth scrolling for navigation links
function initSmoothScrolling() {
    const navLinks = document.querySelectorAll('.nav-menu a[href^="#"]');
    
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            const targetId = link.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 80;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Button effects initialization
function initButtonEffects() {
    const buttons = utils.$$('.btn');

    buttons.forEach(button => {
        if (!button) return;

        // Add ripple effect on click
        button.addEventListener('click', function(e) {
            try {
                // Create ripple effect
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    if (ripple.parentNode) {
                        ripple.remove();
                    }
                }, 600);
            } catch (error) {
                console.error('Button ripple effect error:', error);
            }
        });

        // Add keyboard support
        button.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    });
}

// Accessibility improvements
function initAccessibility() {
    try {
        // Add ARIA labels dynamically where needed
        const productCards = utils.$$('.product-card');
        productCards.forEach((card, index) => {
            if (card && !card.getAttribute('aria-label')) {
                const title = card.querySelector('h3');
                const price = card.querySelector('.price');
                if (title && price) {
                    card.setAttribute('aria-label', `${title.textContent} - ${price.textContent}`);
                }
            }
        });

        // Improve keyboard navigation
        const focusableElements = utils.$$('a, button, [tabindex]:not([tabindex="-1"])');
        focusableElements.forEach(element => {
            if (!element.getAttribute('role') && element.tagName === 'A' && element.getAttribute('href')?.startsWith('#')) {
                element.setAttribute('role', 'button');
            }
        });

        // Add skip links functionality
        const skipLink = utils.$('.skip-link');
        if (skipLink) {
            skipLink.addEventListener('click', (e) => {
                e.preventDefault();
                const target = utils.$(skipLink.getAttribute('href'));
                if (target) {
                    target.focus();
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        }
    } catch (error) {
        console.error('Accessibility initialization error:', error);
    }
}

// Performance optimization - throttled animation updates
let animationId;
function updateAnimations() {
    // Update any continuous animations here
    animationId = requestAnimationFrame(updateAnimations);
}

// Pause animations when tab is not visible
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        cancelAnimationFrame(animationId);
    } else {
        animationId = requestAnimationFrame(updateAnimations);
    }
});

// Enhanced loading animation with error handling
window.addEventListener('load', () => {
    try {
        document.body.classList.add('loaded');

        // Trigger hero animations with safety checks
        setTimeout(() => {
            const heroContent = utils.$('.hero-content');
            if (heroContent) {
                heroContent.style.opacity = '1';
                heroContent.style.transform = 'translateY(0)';
            }
        }, 100);
    } catch (error) {
        console.error('Load animation error:', error);
    }
});

// Cursor trail effect (optional enhancement)
function initCursorTrail() {
    const trail = [];
    const trailLength = 20;
    
    for (let i = 0; i < trailLength; i++) {
        const dot = document.createElement('div');
        dot.className = 'cursor-trail';
        dot.style.cssText = `
            position: fixed;
            width: 4px;
            height: 4px;
            background: #007aff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            opacity: ${1 - i / trailLength};
            transition: all 0.1s ease;
        `;
        document.body.appendChild(dot);
        trail.push(dot);
    }
    
    let mouseX = 0, mouseY = 0;
    
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
    });
    
    function updateTrail() {
        trail.forEach((dot, index) => {
            if (index === 0) {
                dot.style.left = mouseX + 'px';
                dot.style.top = mouseY + 'px';
            } else {
                const prevDot = trail[index - 1];
                const prevX = parseFloat(prevDot.style.left);
                const prevY = parseFloat(prevDot.style.top);
                
                dot.style.left = prevX + 'px';
                dot.style.top = prevY + 'px';
            }
        });
        
        requestAnimationFrame(updateTrail);
    }
    
    updateTrail();
}

// Initialize cursor trail on desktop devices
if (window.innerWidth > 768) {
    initCursorTrail();
}

// Add CSS for scroll animations
const style = document.createElement('style');
style.textContent = `
    .animate-on-scroll {
        opacity: 0;
        transform: translateY(50px);
        transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .animate-on-scroll.animate-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .navbar.scrolled {
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    }
    
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }
    
    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    body.loaded .hero-title-line {
        animation-play-state: running;
    }
`;
document.head.appendChild(style);
