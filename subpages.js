/**
 * Sub-pages specific JavaScript functionality
 * Enhanced interactions for products, services, support, and about pages
 */

// Sub-pages initialization
document.addEventListener('DOMContentLoaded', function() {
    try {
        initSubPageFeatures();
        initFormHandling();
        initSearchFunctionality();
        initFAQInteractions();
        initProductComparison();
        initServicePlanSelection();
        
        console.log('Sub-pages functionality initialized');
    } catch (error) {
        console.error('Sub-pages initialization error:', error);
    }
});

// Initialize sub-page specific features
function initSubPageFeatures() {
    // Product category filtering
    initProductFiltering();
    
    // Service plan comparison
    initPlanComparison();
    
    // Download tracking
    initDownloadTracking();
    
    // Contact method selection
    initContactMethods();
}

// Product filtering functionality
function initProductFiltering() {
    const categoryCards = document.querySelectorAll('.category-card');
    
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.dataset.category;
            if (category) {
                filterProducts(category);
            }
        });
    });
}

function filterProducts(category) {
    const productCards = document.querySelectorAll('.product-detail-card');
    
    productCards.forEach(card => {
        const productCategory = card.dataset.category;
        if (!productCategory || productCategory === category || category === 'all') {
            card.style.display = 'block';
            card.classList.add('animate-in');
        } else {
            card.style.display = 'none';
        }
    });
}

// Form handling
function initFormHandling() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            handleFormSubmission(this);
        });
    });
}

function handleFormSubmission(form) {
    try {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Validate form data
        if (validateFormData(data)) {
            // Show loading state
            showFormLoading(form);
            
            // Simulate form submission
            setTimeout(() => {
                showFormSuccess(form);
                form.reset();
            }, 2000);
        }
    } catch (error) {
        console.error('Form submission error:', error);
        showFormError(form, '提交失败，请稍后重试');
    }
}

function validateFormData(data) {
    const requiredFields = ['name', 'email', 'message'];
    
    for (const field of requiredFields) {
        if (!data[field] || data[field].trim() === '') {
            showFormError(null, `请填写${field === 'name' ? '姓名' : field === 'email' ? '邮箱' : '详细描述'}`);
            return false;
        }
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
        showFormError(null, '请输入有效的邮箱地址');
        return false;
    }
    
    return true;
}

function showFormLoading(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.textContent = '提交中...';
    }
}

function showFormSuccess(form) {
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = false;
        submitBtn.textContent = '提交咨询';
    }
    
    showNotification('提交成功！我们会尽快与您联系。', 'success');
}

function showFormError(form, message) {
    if (form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.textContent = '提交咨询';
        }
    }
    
    showNotification(message, 'error');
}

// Search functionality
function initSearchFunctionality() {
    const searchInputs = document.querySelectorAll('.search-box input');
    
    searchInputs.forEach(input => {
        input.addEventListener('input', debounce(function() {
            const query = this.value.toLowerCase().trim();
            performSearch(query);
        }, 300));
    });
    
    const searchButtons = document.querySelectorAll('.search-box button');
    searchButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.parentElement.querySelector('input');
            if (input) {
                const query = input.value.toLowerCase().trim();
                performSearch(query);
            }
        });
    });
}

function performSearch(query) {
    if (query.length < 2) return;
    
    const searchableElements = document.querySelectorAll('.faq-item, .help-category, .download-item');
    let hasResults = false;
    
    searchableElements.forEach(element => {
        const text = element.textContent.toLowerCase();
        if (text.includes(query)) {
            element.style.display = 'block';
            element.classList.add('search-highlight');
            hasResults = true;
        } else {
            element.style.display = 'none';
            element.classList.remove('search-highlight');
        }
    });
    
    if (!hasResults && query.length > 0) {
        showNotification('未找到相关内容，请尝试其他关键词', 'info');
    }
}

// FAQ interactions
function initFAQInteractions() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const summary = item.querySelector('summary');
        if (summary) {
            summary.addEventListener('click', function() {
                // Close other open FAQ items (accordion behavior)
                faqItems.forEach(otherItem => {
                    if (otherItem !== item && otherItem.hasAttribute('open')) {
                        otherItem.removeAttribute('open');
                    }
                });
            });
        }
    });
}

// Product comparison
function initProductComparison() {
    const comparisonTable = document.querySelector('.comparison-table');
    if (!comparisonTable) return;
    
    // Add hover effects for table rows
    const rows = comparisonTable.querySelectorAll('tbody tr');
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(0, 122, 255, 0.1)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
}

// Service plan selection
function initServicePlanSelection() {
    const planCards = document.querySelectorAll('.plan-card');
    
    planCards.forEach(card => {
        const button = card.querySelector('.btn');
        if (button) {
            button.addEventListener('click', function() {
                const planName = card.querySelector('h3').textContent;
                handlePlanSelection(planName, card);
            });
        }
    });
}

function handlePlanSelection(planName, card) {
    // Highlight selected plan
    document.querySelectorAll('.plan-card').forEach(c => c.classList.remove('selected'));
    card.classList.add('selected');
    
    showNotification(`您选择了 ${planName} 套餐，我们会为您安排专人联系`, 'success');
}

// Download tracking
function initDownloadTracking() {
    const downloadButtons = document.querySelectorAll('.download-item .btn');
    
    downloadButtons.forEach(button => {
        button.addEventListener('click', function() {
            const downloadItem = this.closest('.download-item');
            const fileName = downloadItem.querySelector('h3').textContent;
            
            trackDownload(fileName);
            showNotification(`开始下载 ${fileName}`, 'info');
        });
    });
}

function trackDownload(fileName) {
    // In a real application, you would send this to analytics
    console.log('Download tracked:', fileName);
    
    // Update download count (if available)
    if (window.performanceMonitor) {
        window.performanceMonitor.trackEvent('download', fileName);
    }
}

// Contact methods
function initContactMethods() {
    const contactButtons = document.querySelectorAll('.support-option .btn');
    
    contactButtons.forEach(button => {
        button.addEventListener('click', function() {
            const supportOption = this.closest('.support-option');
            const method = supportOption.querySelector('h3').textContent;
            
            handleContactMethod(method);
        });
    });
}

function handleContactMethod(method) {
    switch (method) {
        case '电话支持':
            showNotification('正在为您转接客服电话...', 'info');
            // In real app: window.location.href = 'tel:************';
            break;
        case '邮件支持':
            showNotification('正在打开邮件客户端...', 'info');
            // In real app: window.location.href = 'mailto:<EMAIL>';
            break;
        case '在线客服':
            showNotification('正在连接在线客服...', 'info');
            // In real app: open chat widget
            break;
    }
}

// Plan comparison functionality
function initPlanComparison() {
    const planCards = document.querySelectorAll('.plan-card');
    let selectedPlans = [];
    
    planCards.forEach(card => {
        // Add comparison checkbox
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'plan-compare-checkbox';
        checkbox.style.position = 'absolute';
        checkbox.style.top = '10px';
        checkbox.style.right = '10px';
        
        card.style.position = 'relative';
        card.appendChild(checkbox);
        
        checkbox.addEventListener('change', function() {
            const planName = card.querySelector('h3').textContent;
            
            if (this.checked) {
                selectedPlans.push(planName);
                card.classList.add('comparing');
            } else {
                selectedPlans = selectedPlans.filter(name => name !== planName);
                card.classList.remove('comparing');
            }
            
            updateComparisonUI();
        });
    });
}

function updateComparisonUI() {
    // This would show/hide a comparison panel
    console.log('Plans selected for comparison:', selectedPlans);
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 24px',
        borderRadius: '8px',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        maxWidth: '300px',
        wordWrap: 'break-word'
    });
    
    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.backgroundColor = '#4CAF50';
            break;
        case 'error':
            notification.style.backgroundColor = '#f44336';
            break;
        case 'info':
        default:
            notification.style.backgroundColor = '#2196F3';
            break;
    }
    
    // Add to page
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after delay
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Export for debugging
window.subPagesUtils = {
    filterProducts,
    performSearch,
    showNotification,
    trackDownload
};
