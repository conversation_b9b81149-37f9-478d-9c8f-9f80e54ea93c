/* CSS Custom Properties */
:root {
    /* Colors */
    --primary-color: #007aff;
    --primary-dark: #0056cc;
    --secondary-color: #5856d6;
    --text-primary: #1d1d1f;
    --text-secondary: #86868b;
    --background-primary: #ffffff;
    --background-secondary: #f5f5f7;
    --background-dark: #1d1d1f;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* Typography */
    --font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;
    --font-size-5xl: 3rem;

    /* Shadows */
    --shadow-sm: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 10px 40px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 20px 60px rgba(0, 0, 0, 0.15);
    --shadow-primary: 0 4px 20px rgba(0, 122, 255, 0.3);

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 20px;
    --radius-xl: 30px;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-base: 0.3s ease;
    --transition-slow: 0.5s ease;
    --transition-cubic: 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    /* Z-index */
    --z-loading: 10000;
    --z-navbar: 1000;
    --z-modal: 9999;
    --z-tooltip: 9998;
}

/* Reset and Base Styles */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

@media (prefers-reduced-motion: reduce) {
    html {
        scroll-behavior: auto;
    }
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Accessibility */
.visually-hidden {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    text-decoration: none;
    border-radius: var(--radius-sm);
    z-index: var(--z-modal);
    transition: top var(--transition-fast);
}

.skip-link:focus {
    top: 6px;
}

/* Focus styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

button:focus,
a:focus {
    outline-offset: 4px;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-loading);
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
    will-change: opacity, visibility;
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-logo {
    margin-bottom: var(--spacing-xl);
    animation: pulse 2s ease-in-out infinite;
}

.loading-logo svg {
    color: white;
    filter: drop-shadow(0 4px 20px rgba(255, 255, 255, 0.3));
    width: 40px;
    height: 48px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    margin: 0 auto var(--spacing-md);
    animation: spin 1s linear infinite;
}

.loading-text {
    font-size: var(--font-size-lg);
    font-weight: 400;
    opacity: 0.9;
    animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes fadeInOut {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: var(--z-navbar);
    transition: all var(--transition-cubic);
    will-change: transform, background-color;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: var(--shadow-sm);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 var(--spacing-md);
    height: 60px;
}

.nav-logo {
    display: inline-flex;
    align-items: center;
    text-decoration: none;
    transition: transform var(--transition-fast);
}

.nav-logo:hover {
    transform: scale(1.05);
}

.nav-logo svg {
    color: var(--text-primary);
    transition: color var(--transition-base);
}

.nav-logo:hover svg {
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 40px;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 400;
    font-size: var(--font-size-sm);
    transition: color var(--transition-base);
    position: relative;
    padding: var(--spacing-xs) 0;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-base);
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-search {
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-search:hover {
    background: rgba(0, 122, 255, 0.1);
    transform: scale(1.1);
}

.nav-search svg {
    color: var(--text-primary);
    transition: color var(--transition-base);
}

.nav-search:hover svg {
    color: var(--primary-color);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 80px var(--spacing-md) 0;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    gap: var(--spacing-xxl);
}

.hero-content {
    flex: 1;
    max-width: 600px;
    opacity: 0;
    transform: translateY(50px);
    animation: fadeInUp 1s ease forwards 0.5s;
}

.hero-title {
    font-size: clamp(var(--font-size-5xl), 8vw, 6rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: var(--spacing-xl);
    overflow: hidden;
}

.hero-title-line {
    display: block;
    transform: translateY(100%);
    animation: slideUp 1s var(--transition-cubic) forwards;
}

.hero-title-line:nth-child(2) {
    animation-delay: 0.2s;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: var(--primary-color); /* Fallback for browsers that don't support background-clip */
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: 40px;
    opacity: 0;
    animation: fadeIn 1s ease forwards 1s;
    max-width: 500px;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    opacity: 0;
    animation: fadeIn 1s ease forwards 1.2s;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-cubic);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    font-family: inherit;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: var(--shadow-primary);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 122, 255, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-link {
    background: none;
    color: var(--primary-color);
    padding: 0;
    font-size: var(--font-size-base);
    border: none;
    cursor: pointer;
    transition: color var(--transition-base);
    min-width: auto;
}

.btn-link:hover:not(:disabled) {
    color: var(--primary-dark);
}

/* Hero Image */
.hero-image {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.floating-device {
    width: 300px;
    height: 400px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 30px;
    position: relative;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    animation: float 6s ease-in-out infinite;
}

.floating-device::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

/* Animations */
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    to {
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(2deg);
    }
}

/* Products Section */
.products {
    padding: 100px 0;
    background: #f5f5f7;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 60px;
    color: #1d1d1f;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.product-card {
    background: white;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 200px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 20px;
    transition: transform 0.3s ease;
}

.product-card:hover .product-placeholder {
    transform: scale(1.05);
}

.product-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.product-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.product-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.product-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #1d1d1f;
}

.product-card p {
    color: #86868b;
    margin-bottom: 20px;
}

.price {
    font-size: 1.25rem;
    font-weight: 600;
    color: #007aff;
}

/* Features Section */
.features {
    padding: 100px 0;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 80px;
    margin-bottom: 120px;
}

.feature-item.reverse {
    flex-direction: row-reverse;
}

.feature-content {
    flex: 1;
}

.feature-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #1d1d1f;
}

.feature-content p {
    font-size: 1.125rem;
    color: #86868b;
    margin-bottom: 30px;
    line-height: 1.6;
}

.feature-visual {
    flex: 1;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.performance-chart {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #007aff 0%, #5856d6 100%);
    border-radius: 20px;
    position: relative;
    overflow: hidden;
}

.performance-chart::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60%;
    background: rgba(255, 255, 255, 0.2);
    animation: chartGrow 2s ease-in-out infinite alternate;
}

.design-showcase {
    width: 250px;
    height: 250px;
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
    border-radius: 50%;
    animation: rotate 10s linear infinite;
    position: relative;
}

.design-showcase::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background: white;
    border-radius: 50%;
}

@keyframes chartGrow {
    0% {
        height: 40%;
    }
    100% {
        height: 80%;
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Footer */
.footer {
    background: #1d1d1f;
    color: white;
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
}

.footer-section ul li a {
    color: #86868b;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid #424245;
    color: #86868b;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero {
        gap: 40px;
    }

    .feature-item {
        gap: 60px;
    }

    .floating-device {
        width: 250px;
        height: 350px;
    }
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 15px;
    }

    .nav-menu {
        display: none;
    }

    .hero {
        flex-direction: column;
        text-align: center;
        padding: 100px 15px 0;
        min-height: 90vh;
    }

    .hero-content {
        margin-bottom: 40px;
        max-width: 100%;
    }

    .hero-title {
        font-size: clamp(2.5rem, 10vw, 4rem);
        margin-bottom: 20px;
    }

    .hero-subtitle {
        font-size: 1.125rem;
        margin-bottom: 30px;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .btn {
        width: 100%;
        max-width: 280px;
        padding: 15px 24px;
    }

    .floating-device {
        width: 200px;
        height: 280px;
    }

    .section-title {
        font-size: 2.5rem;
        margin-bottom: 40px;
    }

    .feature-item,
    .feature-item.reverse {
        flex-direction: column;
        text-align: center;
        gap: 40px;
        margin-bottom: 80px;
    }

    .feature-content h3 {
        font-size: 2rem;
    }

    .feature-content p {
        font-size: 1rem;
    }

    .product-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .product-card {
        padding: 30px 20px;
    }

    .footer-content {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 80px 15px 0;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .feature-content h3 {
        font-size: 1.75rem;
    }

    .product-card {
        padding: 25px 15px;
    }

    .product-placeholder {
        width: 120px;
        height: 120px;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .product-card:hover {
        transform: none;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    }

    .btn:hover {
        transform: none;
    }

    .nav-menu a:hover::after {
        width: 0;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .floating-device,
    .product-placeholder {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-device {
        animation: none;
    }

    .performance-chart::before {
        animation: none;
    }

    .design-showcase {
        animation: none;
    }

    .hero-title-line {
        animation: none;
        transform: translateY(0);
    }

    .loading-logo,
    .loading-spinner,
    .loading-text {
        animation: none;
    }
}

/* Print styles */
@media print {
    .navbar,
    .loading-screen,
    .skip-link {
        display: none !important;
    }

    .hero {
        min-height: auto;
        page-break-after: always;
    }

    .product-card,
    .feature-item {
        page-break-inside: avoid;
    }

    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }
}

/* Error fallbacks */
.hero-content {
    /* Fallback for when JavaScript fails */
    opacity: 1;
    transform: translateY(0);
}

.loading-screen {
    /* Ensure loading screen can be hidden even without JavaScript */
    animation: autoHide 3s ease forwards;
}

@keyframes autoHide {
    0%, 90% {
        opacity: 1;
        visibility: visible;
    }
    100% {
        opacity: 0;
        visibility: hidden;
    }
}

/* Sub-pages Styles */

/* Products Page */
.products-hero,
.services-hero,
.support-hero,
.about-hero {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 120px var(--spacing-md) 80px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    text-align: center;
}

.categories-grid,
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xxl);
}

.category-card,
.service-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
    cursor: pointer;
}

.category-card:hover,
.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.category-image,
.service-icon {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.category-placeholder {
    width: 150px;
    height: 150px;
    border-radius: var(--radius-lg);
    transition: transform var(--transition-base);
}

.category-pro {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.category-air {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.category-mini {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.service-icon svg {
    color: var(--primary-color);
    width: 48px;
    height: 48px;
}

.service-features {
    list-style: none;
    margin: var(--spacing-md) 0;
    text-align: left;
}

.service-features li {
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: var(--spacing-md);
}

.service-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Product Detail Cards */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xxl);
    margin-top: var(--spacing-xxl);
}

.product-detail-card {
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
}

.product-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.product-image-large {
    height: 250px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-secondary);
}

.product-info {
    padding: var(--spacing-xl);
}

.product-tagline {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.product-features ul {
    list-style: none;
    margin: var(--spacing-md) 0;
}

.product-features li {
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: var(--spacing-md);
}

.product-features li::before {
    content: '•';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

.product-pricing {
    margin: var(--spacing-lg) 0;
}

.price-current {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--primary-color);
}

.price-original {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    text-decoration: line-through;
    margin-left: var(--spacing-sm);
}

.product-actions {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

/* Comparison Table */
.comparison-table-wrapper {
    overflow-x: auto;
    margin-top: var(--spacing-xl);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.comparison-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    min-width: 600px;
}

.comparison-table th,
.comparison-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid #eee;
}

.comparison-table th {
    background: var(--background-secondary);
    font-weight: 600;
    color: var(--text-primary);
}

.comparison-table tbody tr:hover {
    background: rgba(0, 122, 255, 0.05);
}

/* Services Page Specific */
.service-process {
    padding: var(--spacing-xxl) 0;
    background: var(--background-secondary);
}

.process-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.process-step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin: 0 auto var(--spacing-md);
}

.process-step h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Service Plans */
.service-plans {
    padding: var(--spacing-xxl) 0;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.plan-card {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
    position: relative;
    text-align: center;
}

.plan-card.featured {
    transform: scale(1.05);
    border: 2px solid var(--primary-color);
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.plan-card.featured:hover {
    transform: scale(1.05) translateY(-5px);
}

.plan-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.plan-header {
    margin-bottom: var(--spacing-lg);
}

.plan-price {
    margin: var(--spacing-md) 0;
}

.plan-price .price {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
}

.plan-price .period {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

.plan-features {
    list-style: none;
    margin: var(--spacing-lg) 0;
    text-align: left;
}

.plan-features li {
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: var(--spacing-lg);
}

.plan-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
}

/* Contact Form */
.service-contact {
    padding: var(--spacing-xxl) 0;
    background: var(--background-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    margin-top: var(--spacing-xl);
}

.contact-methods {
    margin-top: var(--spacing-lg);
}

.contact-method {
    margin-bottom: var(--spacing-lg);
}

.contact-method strong {
    display: block;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.contact-form {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-sm);
    border: 1px solid #ddd;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-base);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Support Page Specific */
.support-search {
    margin-top: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-xxl) var(--spacing-md) var(--spacing-md);
    border: 2px solid white;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-lg);
    background: white;
    box-shadow: var(--shadow-md);
}

.search-box button {
    position: absolute;
    right: var(--spacing-sm);
    background: var(--primary-color);
    border: none;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    color: white;
    cursor: pointer;
    transition: background var(--transition-base);
}

.search-box button:hover {
    background: var(--primary-dark);
}

.help-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.help-category {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
}

.help-category:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.category-icon {
    margin-bottom: var(--spacing-lg);
}

.category-icon svg {
    color: var(--primary-color);
}

/* FAQ Section */
.faq-section {
    padding: var(--spacing-xxl) 0;
}

.faq-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xxl);
    margin-top: var(--spacing-xl);
}

.faq-category h3 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
    font-size: var(--font-size-xl);
}

.faq-item {
    border: 1px solid #eee;
    border-radius: var(--radius-sm);
    margin-bottom: var(--spacing-sm);
    overflow: hidden;
}

.faq-item summary {
    padding: var(--spacing-md);
    background: var(--background-secondary);
    cursor: pointer;
    font-weight: 500;
    transition: background var(--transition-base);
}

.faq-item summary:hover {
    background: #e8e8e8;
}

.faq-content {
    padding: var(--spacing-md);
    background: white;
}

.faq-content ul,
.faq-content ol {
    margin: var(--spacing-sm) 0;
    padding-left: var(--spacing-lg);
}

.faq-content li {
    margin-bottom: var(--spacing-xs);
}

/* Downloads Section */
.downloads-section {
    padding: var(--spacing-xxl) 0;
    background: var(--background-secondary);
}

.downloads-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.download-item {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
}

.download-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.download-icon {
    margin-bottom: var(--spacing-lg);
}

.download-icon svg {
    color: var(--primary-color);
}

.download-meta {
    margin: var(--spacing-md) 0;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.download-meta span {
    display: block;
    margin-bottom: var(--spacing-xs);
}

/* Support Options */
.contact-support {
    padding: var(--spacing-xxl) 0;
}

.support-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.support-option {
    background: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
}

.support-option:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.support-icon {
    margin-bottom: var(--spacing-lg);
}

.support-icon svg {
    color: var(--primary-color);
}

.support-details {
    margin: var(--spacing-lg) 0;
}

.support-details strong {
    display: block;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
}

.support-details span {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* About Page Specific */
.mission-vision {
    padding: var(--spacing-xxl) 0;
}

.mission-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xxl);
    margin-top: var(--spacing-xl);
}

.mission-item,
.vision-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.mission-item h2,
.vision-item h2 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-2xl);
}

/* Company Values */
.company-values {
    padding: var(--spacing-xxl) 0;
    background: var(--background-secondary);
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.value-card {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
}

.value-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.value-icon {
    margin-bottom: var(--spacing-lg);
}

.value-icon svg {
    color: var(--primary-color);
}

/* Timeline */
.company-timeline {
    padding: var(--spacing-xxl) 0;
}

.timeline {
    position: relative;
    margin-top: var(--spacing-xl);
    padding-left: var(--spacing-xl);
}

.timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-xxl);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
}

.timeline-year {
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-weight: 600;
    min-width: 80px;
    text-align: center;
    position: relative;
    z-index: 1;
}

.timeline-content {
    background: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    flex: 1;
}

.timeline-content h3 {
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

/* Team Section */
.team-section {
    padding: var(--spacing-xxl) 0;
    background: var(--background-secondary);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.team-member {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.member-photo {
    margin-bottom: var(--spacing-lg);
}

.photo-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 0 auto;
}

.member-title {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.member-bio {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
}

/* Company Stats */
.company-stats {
    padding: var(--spacing-xxl) 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-lg);
}

.stat-number {
    font-size: var(--font-size-5xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
}

/* News Section */
.news-section {
    padding: var(--spacing-xxl) 0;
    background: var(--background-secondary);
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.news-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-cubic);
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.news-date {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.news-item h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.news-item p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

/* Contact Info */
.contact-info {
    padding: var(--spacing-xxl) 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-top: var(--spacing-xl);
}

.contact-item {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.contact-item h3 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.contact-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Responsive Design for Sub-pages */
@media (max-width: 768px) {
    .products-hero,
    .services-hero,
    .support-hero,
    .about-hero {
        min-height: 50vh;
        padding: 100px var(--spacing-md) 60px;
    }

    .categories-grid,
    .services-grid,
    .help-categories,
    .values-grid,
    .team-grid,
    .stats-grid,
    .news-grid,
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .plans-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .plan-card.featured {
        transform: none;
        order: -1;
    }

    .support-options,
    .downloads-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .faq-categories {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .comparison-table-wrapper {
        margin: var(--spacing-lg) -var(--spacing-md);
    }

    .timeline {
        padding-left: var(--spacing-lg);
    }

    .timeline::before {
        left: 15px;
    }

    .timeline-item {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .timeline-year {
        align-self: flex-start;
        min-width: auto;
    }

    .process-steps {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .section-title {
        font-size: var(--font-size-3xl);
    }

    .category-card,
    .service-card,
    .help-category,
    .value-card,
    .team-member,
    .news-item,
    .contact-item {
        padding: var(--spacing-lg);
    }

    .product-detail-card .product-info {
        padding: var(--spacing-lg);
    }

    .product-actions {
        flex-direction: column;
    }

    .product-actions .btn {
        width: 100%;
    }

    .plan-card {
        padding: var(--spacing-lg);
    }

    .contact-form {
        padding: var(--spacing-lg);
    }

    .process-steps {
        grid-template-columns: 1fr;
    }

    .search-box input {
        font-size: var(--font-size-base);
        padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-sm);
    }

    .stat-number {
        font-size: var(--font-size-4xl);
    }

    .timeline-content {
        padding: var(--spacing-md);
    }
}

/* Navigation active state */
.nav-menu a[aria-current="page"] {
    color: var(--primary-color);
    font-weight: 500;
}

.nav-menu a[aria-current="page"]::after {
    width: 100%;
}

/* Loading states for dynamic content */
.loading-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Accessibility improvements for sub-pages */
.skip-link:focus {
    top: 6px;
    z-index: var(--z-modal);
}

/* Focus management for interactive elements */
.faq-item[open] summary {
    background: #d0d0d0;
}

.download-item:focus-within,
.support-option:focus-within,
.team-member:focus-within {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print styles for sub-pages */
@media print {
    .products-hero,
    .services-hero,
    .support-hero,
    .about-hero {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }

    .category-card,
    .service-card,
    .product-detail-card,
    .plan-card,
    .help-category,
    .value-card,
    .team-member {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc;
    }

    .timeline::before {
        display: none;
    }

    .timeline-item {
        border-left: 2px solid #ccc;
        padding-left: var(--spacing-md);
    }
}
