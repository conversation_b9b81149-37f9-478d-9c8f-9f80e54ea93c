/**
 * Advanced Search and Filter System
 * Intelligent search with fuzzy matching, filters, and suggestions
 */

class AdvancedSearch {
    constructor() {
        this.searchIndex = new Map();
        this.searchHistory = [];
        this.suggestions = [];
        this.filters = {
            category: 'all',
            priceRange: 'all',
            features: []
        };
        
        this.init();
    }

    init() {
        this.buildSearchIndex();
        this.enhanceSearchInputs();
        this.createFilterUI();
        this.loadSearchHistory();
        
        console.log('Advanced search system initialized');
    }

    buildSearchIndex() {
        // Index all searchable content
        const searchableElements = document.querySelectorAll(`
            .product-card, .service-card, .help-category, .faq-item,
            .download-item, .news-item, .team-member, h1, h2, h3
        `);

        searchableElements.forEach((element, index) => {
            const text = this.extractSearchableText(element);
            const keywords = this.generateKeywords(text);
            const metadata = this.extractMetadata(element);

            this.searchIndex.set(index, {
                element,
                text,
                keywords,
                metadata,
                score: 0
            });
        });

        console.log(`Search index built with ${this.searchIndex.size} items`);
    }

    extractSearchableText(element) {
        // Get all text content, excluding hidden elements
        const clone = element.cloneNode(true);
        
        // Remove hidden elements
        clone.querySelectorAll('[aria-hidden="true"], .visually-hidden, .sr-only').forEach(el => {
            el.remove();
        });

        return clone.textContent.toLowerCase().trim();
    }

    generateKeywords(text) {
        // Generate searchable keywords
        const words = text.split(/\s+/)
            .filter(word => word.length > 2)
            .map(word => word.replace(/[^\w\u4e00-\u9fff]/g, ''));

        // Add common synonyms and variations
        const synonyms = {
            '产品': ['商品', '设备', '产品'],
            '服务': ['支持', '帮助', '客服'],
            '技术': ['科技', '技术', 'tech'],
            '支持': ['帮助', '服务', 'support'],
            '价格': ['费用', '成本', '价钱']
        };

        const expandedKeywords = [...words];
        words.forEach(word => {
            if (synonyms[word]) {
                expandedKeywords.push(...synonyms[word]);
            }
        });

        return [...new Set(expandedKeywords)];
    }

    extractMetadata(element) {
        const metadata = {
            type: 'general',
            category: 'all',
            price: null,
            features: []
        };

        // Determine element type
        if (element.classList.contains('product-card') || element.classList.contains('product-detail-card')) {
            metadata.type = 'product';
            metadata.category = element.dataset.category || 'general';
            
            const priceElement = element.querySelector('.price, .price-current');
            if (priceElement) {
                const priceText = priceElement.textContent.replace(/[^\d]/g, '');
                metadata.price = parseInt(priceText) || null;
            }

            const features = element.querySelectorAll('.product-features li, .service-features li');
            metadata.features = Array.from(features).map(li => li.textContent.toLowerCase());
        } else if (element.classList.contains('service-card')) {
            metadata.type = 'service';
        } else if (element.classList.contains('faq-item')) {
            metadata.type = 'faq';
        } else if (element.classList.contains('download-item')) {
            metadata.type = 'download';
        }

        return metadata;
    }

    enhanceSearchInputs() {
        const searchInputs = document.querySelectorAll('input[type="text"], input[placeholder*="搜索"]');
        
        searchInputs.forEach(input => {
            this.enhanceSearchInput(input);
        });
    }

    enhanceSearchInput(input) {
        // Create search container
        const container = document.createElement('div');
        container.className = 'advanced-search-container';
        
        input.parentNode.insertBefore(container, input);
        container.appendChild(input);

        // Add search suggestions dropdown
        const dropdown = document.createElement('div');
        dropdown.className = 'search-suggestions';
        container.appendChild(dropdown);

        // Add search history
        const history = document.createElement('div');
        history.className = 'search-history';
        container.appendChild(history);

        // Enhanced event listeners
        input.addEventListener('input', this.debounce((e) => {
            this.handleSearchInput(e.target, dropdown, history);
        }, 200));

        input.addEventListener('focus', () => {
            this.showSearchHistory(history);
        });

        input.addEventListener('blur', () => {
            // Delay hiding to allow clicking on suggestions
            setTimeout(() => {
                dropdown.style.display = 'none';
                history.style.display = 'none';
            }, 200);
        });

        // Keyboard navigation
        input.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e, dropdown);
        });
    }

    handleSearchInput(input, dropdown, history) {
        const query = input.value.trim();
        
        if (query.length < 2) {
            dropdown.style.display = 'none';
            return;
        }

        const results = this.performAdvancedSearch(query);
        this.showSearchSuggestions(dropdown, results, query);
        
        // Hide history when typing
        history.style.display = 'none';
    }

    performAdvancedSearch(query) {
        const queryWords = query.toLowerCase().split(/\s+/);
        const results = [];

        this.searchIndex.forEach((item, index) => {
            let score = 0;

            // Exact match bonus
            if (item.text.includes(query.toLowerCase())) {
                score += 100;
            }

            // Keyword matching with fuzzy search
            queryWords.forEach(queryWord => {
                item.keywords.forEach(keyword => {
                    const similarity = this.calculateSimilarity(queryWord, keyword);
                    if (similarity > 0.7) {
                        score += similarity * 10;
                    }
                });
            });

            // Title/heading bonus
            const headings = item.element.querySelectorAll('h1, h2, h3, h4, h5, h6');
            headings.forEach(heading => {
                if (heading.textContent.toLowerCase().includes(query.toLowerCase())) {
                    score += 50;
                }
            });

            // Apply filters
            if (this.filters.category !== 'all' && item.metadata.category !== this.filters.category) {
                score *= 0.1;
            }

            if (this.filters.priceRange !== 'all' && item.metadata.price) {
                const inRange = this.isPriceInRange(item.metadata.price, this.filters.priceRange);
                if (!inRange) score *= 0.1;
            }

            if (score > 0) {
                results.push({ ...item, score, index });
            }
        });

        return results.sort((a, b) => b.score - a.score).slice(0, 10);
    }

    calculateSimilarity(str1, str2) {
        // Simple Levenshtein distance-based similarity
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const distance = this.levenshteinDistance(longer, shorter);
        return (longer.length - distance) / longer.length;
    }

    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    showSearchSuggestions(dropdown, results, query) {
        dropdown.innerHTML = '';
        
        if (results.length === 0) {
            dropdown.innerHTML = '<div class="no-results">未找到相关结果</div>';
        } else {
            results.forEach(result => {
                const suggestion = document.createElement('div');
                suggestion.className = 'search-suggestion';
                
                const title = this.extractTitle(result.element);
                const snippet = this.createSnippet(result.text, query);
                
                suggestion.innerHTML = `
                    <div class="suggestion-title">${this.highlightQuery(title, query)}</div>
                    <div class="suggestion-snippet">${snippet}</div>
                    <div class="suggestion-type">${this.getTypeLabel(result.metadata.type)}</div>
                `;
                
                suggestion.addEventListener('click', () => {
                    this.selectSuggestion(result, query);
                });
                
                dropdown.appendChild(suggestion);
            });
        }
        
        dropdown.style.display = 'block';
    }

    extractTitle(element) {
        const heading = element.querySelector('h1, h2, h3, h4, h5, h6');
        return heading ? heading.textContent : element.textContent.substring(0, 50) + '...';
    }

    createSnippet(text, query) {
        const queryIndex = text.toLowerCase().indexOf(query.toLowerCase());
        if (queryIndex === -1) return text.substring(0, 100) + '...';
        
        const start = Math.max(0, queryIndex - 30);
        const end = Math.min(text.length, queryIndex + query.length + 30);
        
        let snippet = text.substring(start, end);
        if (start > 0) snippet = '...' + snippet;
        if (end < text.length) snippet = snippet + '...';
        
        return this.highlightQuery(snippet, query);
    }

    highlightQuery(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    getTypeLabel(type) {
        const labels = {
            'product': '产品',
            'service': '服务',
            'faq': '常见问题',
            'download': '下载',
            'general': '内容'
        };
        return labels[type] || '内容';
    }

    selectSuggestion(result, query) {
        // Scroll to element
        result.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // Highlight element temporarily
        result.element.classList.add('search-highlight');
        setTimeout(() => {
            result.element.classList.remove('search-highlight');
        }, 3000);
        
        // Save to search history
        this.addToSearchHistory(query);
        
        // Track search
        this.trackSearch(query, result.metadata.type);
    }

    addToSearchHistory(query) {
        if (!this.searchHistory.includes(query)) {
            this.searchHistory.unshift(query);
            this.searchHistory = this.searchHistory.slice(0, 10); // Keep last 10
            this.saveSearchHistory();
        }
    }

    showSearchHistory(historyElement) {
        if (this.searchHistory.length === 0) return;
        
        historyElement.innerHTML = '<div class="history-title">搜索历史</div>';
        
        this.searchHistory.forEach(query => {
            const item = document.createElement('div');
            item.className = 'history-item';
            item.textContent = query;
            
            item.addEventListener('click', () => {
                const input = historyElement.parentNode.querySelector('input');
                input.value = query;
                input.dispatchEvent(new Event('input'));
            });
            
            historyElement.appendChild(item);
        });
        
        historyElement.style.display = 'block';
    }

    createFilterUI() {
        // This would create advanced filter controls
        // Implementation depends on specific page requirements
        console.log('Filter UI would be created here');
    }

    isPriceInRange(price, range) {
        const ranges = {
            'low': [0, 5000],
            'medium': [5000, 10000],
            'high': [10000, Infinity]
        };
        
        if (!ranges[range]) return true;
        
        const [min, max] = ranges[range];
        return price >= min && price < max;
    }

    loadSearchHistory() {
        try {
            const saved = localStorage.getItem('search-history');
            if (saved) {
                this.searchHistory = JSON.parse(saved);
            }
        } catch (error) {
            console.warn('Failed to load search history:', error);
        }
    }

    saveSearchHistory() {
        try {
            localStorage.setItem('search-history', JSON.stringify(this.searchHistory));
        } catch (error) {
            console.warn('Failed to save search history:', error);
        }
    }

    trackSearch(query, resultType) {
        if (window.performanceMonitor) {
            window.performanceMonitor.trackEvent('search', { query, resultType });
        }
    }

    handleKeyboardNavigation(e, dropdown) {
        const suggestions = dropdown.querySelectorAll('.search-suggestion');
        if (suggestions.length === 0) return;
        
        const current = dropdown.querySelector('.search-suggestion.selected');
        let index = current ? Array.from(suggestions).indexOf(current) : -1;
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            index = (index + 1) % suggestions.length;
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            index = index <= 0 ? suggestions.length - 1 : index - 1;
        } else if (e.key === 'Enter' && current) {
            e.preventDefault();
            current.click();
            return;
        } else {
            return;
        }
        
        // Update selection
        suggestions.forEach(s => s.classList.remove('selected'));
        suggestions[index].classList.add('selected');
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Public API
    search(query) {
        return this.performAdvancedSearch(query);
    }

    setFilter(filterType, value) {
        this.filters[filterType] = value;
    }

    clearHistory() {
        this.searchHistory = [];
        this.saveSearchHistory();
    }
}

// Initialize advanced search
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.advancedSearch = new AdvancedSearch();
        console.log('Advanced search system loaded');
    } catch (error) {
        console.error('Failed to initialize advanced search:', error);
    }
});

// Export for debugging
window.AdvancedSearch = AdvancedSearch;
