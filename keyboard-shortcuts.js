/**
 * Keyboard Shortcuts System
 * Advanced keyboard navigation and shortcuts
 */

class KeyboardShortcuts {
    constructor() {
        this.shortcuts = new Map();
        this.isEnabled = true;
        this.helpVisible = false;
        this.focusableElements = [];
        this.currentFocusIndex = -1;
        
        this.init();
    }

    init() {
        this.registerDefaultShortcuts();
        this.setupEventListeners();
        this.createHelpOverlay();
        this.updateFocusableElements();
        
        console.log('Keyboard shortcuts system initialized');
    }

    registerDefaultShortcuts() {
        // Navigation shortcuts
        this.register('h', () => this.navigateToPage('index.html'), '返回首页');
        this.register('p', () => this.navigateToPage('products.html'), '产品页面');
        this.register('s', () => this.navigateToPage('services.html'), '服务页面');
        this.register('u', () => this.navigateToPage('support.html'), '支持页面');
        this.register('a', () => this.navigateToPage('about.html'), '关于页面');

        // Search shortcuts
        this.register('/', () => this.focusSearch(), '聚焦搜索框');
        this.register('ctrl+k', () => this.focusSearch(), '聚焦搜索框');
        this.register('cmd+k', () => this.focusSearch(), '聚焦搜索框');

        // Theme shortcuts
        this.register('ctrl+shift+d', () => this.toggleTheme(), '切换深色模式');
        this.register('cmd+shift+d', () => this.toggleTheme(), '切换深色模式');

        // Accessibility shortcuts
        this.register('ctrl+shift+a', () => this.toggleAccessibilityPanel(), '可访问性设置');
        this.register('cmd+shift+a', () => this.toggleAccessibilityPanel(), '可访问性设置');

        // Navigation shortcuts
        this.register('j', () => this.navigateNext(), '下一个元素');
        this.register('k', () => this.navigatePrevious(), '上一个元素');
        this.register('g g', () => this.scrollToTop(), '回到顶部');
        this.register('g e', () => this.scrollToBottom(), '滚动到底部');

        // Help
        this.register('?', () => this.toggleHelp(), '显示/隐藏快捷键帮助');
        this.register('escape', () => this.handleEscape(), '关闭弹窗/取消操作');

        // Product shortcuts (when on products page)
        if (window.location.pathname.includes('products')) {
            this.register('1', () => this.selectProduct(0), '选择第一个产品');
            this.register('2', () => this.selectProduct(1), '选择第二个产品');
            this.register('3', () => this.selectProduct(2), '选择第三个产品');
            this.register('c', () => this.toggleComparison(), '切换产品对比');
        }

        // FAQ shortcuts (when on support page)
        if (window.location.pathname.includes('support')) {
            this.register('f', () => this.focusFirstFAQ(), '聚焦第一个FAQ');
            this.register('enter', () => this.toggleCurrentFAQ(), '展开/收起当前FAQ');
        }
    }

    register(keys, callback, description) {
        const normalizedKeys = this.normalizeKeys(keys);
        this.shortcuts.set(normalizedKeys, { callback, description, keys });
    }

    normalizeKeys(keys) {
        return keys.toLowerCase()
            .replace(/\s+/g, ' ')
            .replace(/cmd/g, 'meta')
            .split(' ')
            .sort()
            .join(' ');
    }

    setupEventListeners() {
        let keySequence = [];
        let sequenceTimer = null;

        document.addEventListener('keydown', (e) => {
            if (!this.isEnabled) return;

            // Don't interfere with form inputs (unless it's a specific shortcut)
            if (this.isTyping(e.target) && !this.isGlobalShortcut(e)) {
                return;
            }

            const key = this.getKeyString(e);
            
            // Handle escape key specially
            if (key === 'escape') {
                this.handleEscape();
                return;
            }

            // Clear sequence timer
            if (sequenceTimer) {
                clearTimeout(sequenceTimer);
            }

            // Add key to sequence
            keySequence.push(key);

            // Try to match shortcuts
            const matched = this.matchShortcut(keySequence);
            
            if (matched === 'exact') {
                e.preventDefault();
                keySequence = [];
            } else if (matched === 'partial') {
                e.preventDefault();
                // Wait for more keys
                sequenceTimer = setTimeout(() => {
                    keySequence = [];
                }, 1000);
            } else {
                // No match, reset sequence
                keySequence = [];
            }
        });

        // Update focusable elements when DOM changes
        const observer = new MutationObserver(() => {
            this.updateFocusableElements();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    getKeyString(e) {
        const parts = [];
        
        if (e.ctrlKey) parts.push('ctrl');
        if (e.altKey) parts.push('alt');
        if (e.shiftKey) parts.push('shift');
        if (e.metaKey) parts.push('meta');
        
        const key = e.key.toLowerCase();
        if (key !== 'control' && key !== 'alt' && key !== 'shift' && key !== 'meta') {
            parts.push(key);
        }
        
        return parts.join('+');
    }

    isTyping(element) {
        const typingElements = ['input', 'textarea', 'select'];
        const isContentEditable = element.contentEditable === 'true';
        const isTypingElement = typingElements.includes(element.tagName.toLowerCase());
        
        return isContentEditable || isTypingElement;
    }

    isGlobalShortcut(e) {
        const globalShortcuts = [
            'ctrl+k', 'cmd+k', 'ctrl+shift+d', 'cmd+shift+d',
            'ctrl+shift+a', 'cmd+shift+a', 'escape'
        ];
        
        const keyString = this.getKeyString(e);
        return globalShortcuts.includes(keyString);
    }

    matchShortcut(keySequence) {
        const sequenceString = keySequence.join(' ');
        
        // Check for exact match
        if (this.shortcuts.has(sequenceString)) {
            const shortcut = this.shortcuts.get(sequenceString);
            shortcut.callback();
            return 'exact';
        }
        
        // Check for partial match
        for (const [keys] of this.shortcuts) {
            if (keys.startsWith(sequenceString) && keys !== sequenceString) {
                return 'partial';
            }
        }
        
        return 'none';
    }

    // Navigation methods
    navigateToPage(page) {
        if (window.location.pathname.endsWith(page)) return;
        window.location.href = page;
    }

    focusSearch() {
        const searchInput = document.querySelector('input[type="text"], input[placeholder*="搜索"]');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }

    toggleTheme() {
        if (window.themeSystem) {
            window.themeSystem.toggleTheme();
        }
    }

    toggleAccessibilityPanel() {
        const panel = document.querySelector('.accessibility-menu');
        if (panel) {
            panel.classList.toggle('open');
        }
    }

    updateFocusableElements() {
        this.focusableElements = Array.from(document.querySelectorAll(`
            a[href], button:not([disabled]), input:not([disabled]), 
            textarea:not([disabled]), select:not([disabled]), 
            [tabindex]:not([tabindex="-1"]), [contenteditable="true"]
        `)).filter(el => {
            return el.offsetParent !== null && !el.hasAttribute('aria-hidden');
        });
    }

    navigateNext() {
        if (this.focusableElements.length === 0) return;
        
        this.currentFocusIndex = (this.currentFocusIndex + 1) % this.focusableElements.length;
        this.focusableElements[this.currentFocusIndex].focus();
        this.scrollIntoViewIfNeeded(this.focusableElements[this.currentFocusIndex]);
    }

    navigatePrevious() {
        if (this.focusableElements.length === 0) return;
        
        this.currentFocusIndex = this.currentFocusIndex <= 0 
            ? this.focusableElements.length - 1 
            : this.currentFocusIndex - 1;
        this.focusableElements[this.currentFocusIndex].focus();
        this.scrollIntoViewIfNeeded(this.focusableElements[this.currentFocusIndex]);
    }

    scrollIntoViewIfNeeded(element) {
        const rect = element.getBoundingClientRect();
        const isVisible = rect.top >= 0 && rect.bottom <= window.innerHeight;
        
        if (!isVisible) {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    scrollToBottom() {
        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
    }

    // Page-specific methods
    selectProduct(index) {
        const products = document.querySelectorAll('.product-card, .product-detail-card');
        if (products[index]) {
            products[index].focus();
            products[index].scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    toggleComparison() {
        const compareCheckboxes = document.querySelectorAll('.plan-compare-checkbox');
        if (compareCheckboxes.length > 0) {
            compareCheckboxes[0].click();
        }
    }

    focusFirstFAQ() {
        const firstFAQ = document.querySelector('.faq-item summary');
        if (firstFAQ) {
            firstFAQ.focus();
        }
    }

    toggleCurrentFAQ() {
        const focused = document.activeElement;
        if (focused && focused.tagName === 'SUMMARY') {
            focused.click();
        }
    }

    handleEscape() {
        // Close any open modals, dropdowns, etc.
        const openElements = document.querySelectorAll('.open, [aria-expanded="true"]');
        openElements.forEach(el => {
            if (el.classList.contains('accessibility-menu')) {
                el.classList.remove('open');
            } else if (el.hasAttribute('aria-expanded')) {
                el.setAttribute('aria-expanded', 'false');
            }
        });

        // Hide help if visible
        if (this.helpVisible) {
            this.toggleHelp();
        }

        // Clear search if focused
        const searchInput = document.querySelector('input[type="text"]:focus');
        if (searchInput) {
            searchInput.value = '';
            searchInput.blur();
        }
    }

    createHelpOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'keyboard-help-overlay';
        overlay.innerHTML = `
            <div class="keyboard-help-content">
                <div class="help-header">
                    <h2>键盘快捷键</h2>
                    <button class="help-close" aria-label="关闭帮助">×</button>
                </div>
                <div class="help-sections">
                    <div class="help-section">
                        <h3>导航</h3>
                        <div class="shortcuts-list" id="navigation-shortcuts"></div>
                    </div>
                    <div class="help-section">
                        <h3>搜索</h3>
                        <div class="shortcuts-list" id="search-shortcuts"></div>
                    </div>
                    <div class="help-section">
                        <h3>主题</h3>
                        <div class="shortcuts-list" id="theme-shortcuts"></div>
                    </div>
                    <div class="help-section">
                        <h3>其他</h3>
                        <div class="shortcuts-list" id="other-shortcuts"></div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(overlay);
        this.helpOverlay = overlay;

        // Populate shortcuts
        this.populateHelpContent();

        // Event listeners
        overlay.querySelector('.help-close').addEventListener('click', () => {
            this.toggleHelp();
        });

        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.toggleHelp();
            }
        });
    }

    populateHelpContent() {
        const categories = {
            navigation: ['h', 'p', 's', 'u', 'a', 'j', 'k', 'g g', 'g e'],
            search: ['/', 'ctrl+k', 'cmd+k'],
            theme: ['ctrl+shift+d', 'cmd+shift+d', 'ctrl+shift+a', 'cmd+shift+a'],
            other: ['?', 'escape', '1', '2', '3', 'c', 'f', 'enter']
        };

        Object.entries(categories).forEach(([category, keys]) => {
            const container = document.getElementById(`${category}-shortcuts`);
            if (!container) return;

            keys.forEach(key => {
                const shortcut = this.shortcuts.get(this.normalizeKeys(key));
                if (shortcut) {
                    const item = document.createElement('div');
                    item.className = 'shortcut-item';
                    item.innerHTML = `
                        <kbd>${this.formatKeys(shortcut.keys)}</kbd>
                        <span>${shortcut.description}</span>
                    `;
                    container.appendChild(item);
                }
            });
        });
    }

    formatKeys(keys) {
        return keys.replace(/\+/g, ' + ')
            .replace(/ctrl/g, 'Ctrl')
            .replace(/cmd/g, 'Cmd')
            .replace(/shift/g, 'Shift')
            .replace(/alt/g, 'Alt')
            .replace(/meta/g, 'Cmd');
    }

    toggleHelp() {
        this.helpVisible = !this.helpVisible;
        this.helpOverlay.style.display = this.helpVisible ? 'flex' : 'none';
        
        if (this.helpVisible) {
            this.helpOverlay.querySelector('.help-close').focus();
        }
    }

    // Public API
    enable() {
        this.isEnabled = true;
    }

    disable() {
        this.isEnabled = false;
    }

    addShortcut(keys, callback, description) {
        this.register(keys, callback, description);
    }

    removeShortcut(keys) {
        const normalizedKeys = this.normalizeKeys(keys);
        this.shortcuts.delete(normalizedKeys);
    }

    getShortcuts() {
        return Array.from(this.shortcuts.entries()).map(([keys, data]) => ({
            keys,
            description: data.description
        }));
    }
}

// Add CSS for keyboard help
const style = document.createElement('style');
style.textContent = `
    .keyboard-help-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 20px;
    }

    .keyboard-help-content {
        background: var(--background-primary);
        border-radius: 12px;
        max-width: 800px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    }

    .help-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px;
        border-bottom: 1px solid var(--border-color);
    }

    .help-header h2 {
        margin: 0;
        color: var(--text-primary);
    }

    .help-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-secondary);
        padding: 4px;
        border-radius: 4px;
    }

    .help-close:hover {
        background: var(--background-secondary);
        color: var(--text-primary);
    }

    .help-sections {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 24px;
        padding: 24px;
    }

    .help-section h3 {
        margin: 0 0 12px 0;
        color: var(--primary-color);
        font-size: 16px;
    }

    .shortcut-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid var(--border-color);
    }

    .shortcut-item:last-child {
        border-bottom: none;
    }

    .shortcut-item kbd {
        background: var(--background-secondary);
        border: 1px solid var(--border-color);
        border-radius: 4px;
        padding: 2px 6px;
        font-family: monospace;
        font-size: 12px;
        color: var(--text-primary);
    }

    .shortcut-item span {
        color: var(--text-secondary);
        font-size: 14px;
    }

    /* Focus indicators */
    *:focus {
        outline: 2px solid var(--primary-color);
        outline-offset: 2px;
    }

    /* Keyboard navigation highlight */
    .keyboard-focus {
        box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.3);
    }
`;
document.head.appendChild(style);

// Initialize keyboard shortcuts
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.keyboardShortcuts = new KeyboardShortcuts();
        console.log('Keyboard shortcuts system loaded');
    } catch (error) {
        console.error('Failed to initialize keyboard shortcuts:', error);
    }
});

// Export for debugging
window.KeyboardShortcuts = KeyboardShortcuts;
