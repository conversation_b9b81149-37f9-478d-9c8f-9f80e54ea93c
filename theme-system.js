/**
 * Advanced Theme System
 * Supports dark mode, high contrast, and accessibility preferences
 */

class ThemeSystem {
    constructor() {
        this.currentTheme = 'light';
        this.preferences = {
            reducedMotion: false,
            highContrast: false,
            fontSize: 'normal'
        };
        
        this.init();
    }

    init() {
        this.loadSavedPreferences();
        this.detectSystemPreferences();
        this.createThemeControls();
        this.applyTheme();
        this.setupEventListeners();
        
        console.log('Theme system initialized');
    }

    loadSavedPreferences() {
        try {
            const saved = localStorage.getItem('theme-preferences');
            if (saved) {
                const preferences = JSON.parse(saved);
                this.currentTheme = preferences.theme || 'light';
                this.preferences = { ...this.preferences, ...preferences.preferences };
            }
        } catch (error) {
            console.warn('Failed to load theme preferences:', error);
        }
    }

    detectSystemPreferences() {
        // Detect system dark mode preference
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            if (!localStorage.getItem('theme-preferences')) {
                this.currentTheme = 'dark';
            }
        }

        // Detect reduced motion preference
        if (window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            this.preferences.reducedMotion = true;
        }

        // Detect high contrast preference
        if (window.matchMedia && window.matchMedia('(prefers-contrast: high)').matches) {
            this.preferences.highContrast = true;
        }

        // Listen for system preference changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (!localStorage.getItem('theme-preferences')) {
                    this.currentTheme = e.matches ? 'dark' : 'light';
                    this.applyTheme();
                }
            });

            window.matchMedia('(prefers-reduced-motion: reduce)').addEventListener('change', (e) => {
                this.preferences.reducedMotion = e.matches;
                this.applyTheme();
            });
        }
    }

    createThemeControls() {
        // Create theme toggle button
        const themeToggle = document.createElement('button');
        themeToggle.className = 'theme-toggle';
        themeToggle.setAttribute('aria-label', '切换主题');
        themeToggle.innerHTML = this.getThemeIcon();
        
        // Add to navigation
        const navContainer = document.querySelector('.nav-container');
        if (navContainer) {
            navContainer.appendChild(themeToggle);
        }

        // Create accessibility panel
        this.createAccessibilityPanel();

        // Store reference
        this.themeToggle = themeToggle;
    }

    createAccessibilityPanel() {
        const panel = document.createElement('div');
        panel.className = 'accessibility-panel';
        panel.innerHTML = `
            <button class="accessibility-toggle" aria-label="可访问性设置">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2c1.1 0 2 .9 2 2s-.9 2-2 2-2-.9-2-2 .9-2 2-2zm9 7h-6v13h-2v-6h-2v6H9V9H3V7h18v2z"/>
                </svg>
            </button>
            <div class="accessibility-menu">
                <h3>可访问性设置</h3>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="high-contrast" ${this.preferences.highContrast ? 'checked' : ''}>
                        高对比度模式
                    </label>
                </div>
                <div class="setting-group">
                    <label>
                        <input type="checkbox" id="reduced-motion" ${this.preferences.reducedMotion ? 'checked' : ''}>
                        减少动画效果
                    </label>
                </div>
                <div class="setting-group">
                    <label for="font-size">字体大小</label>
                    <select id="font-size">
                        <option value="small" ${this.preferences.fontSize === 'small' ? 'selected' : ''}>小</option>
                        <option value="normal" ${this.preferences.fontSize === 'normal' ? 'selected' : ''}>正常</option>
                        <option value="large" ${this.preferences.fontSize === 'large' ? 'selected' : ''}>大</option>
                        <option value="extra-large" ${this.preferences.fontSize === 'extra-large' ? 'selected' : ''}>特大</option>
                    </select>
                </div>
                <button class="reset-settings">重置设置</button>
            </div>
        `;

        document.body.appendChild(panel);
        this.accessibilityPanel = panel;
    }

    setupEventListeners() {
        // Theme toggle
        if (this.themeToggle) {
            this.themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Accessibility panel
        if (this.accessibilityPanel) {
            const toggle = this.accessibilityPanel.querySelector('.accessibility-toggle');
            const menu = this.accessibilityPanel.querySelector('.accessibility-menu');

            toggle.addEventListener('click', () => {
                menu.classList.toggle('open');
            });

            // Close panel when clicking outside
            document.addEventListener('click', (e) => {
                if (!this.accessibilityPanel.contains(e.target)) {
                    menu.classList.remove('open');
                }
            });

            // Settings controls
            const highContrastToggle = this.accessibilityPanel.querySelector('#high-contrast');
            const reducedMotionToggle = this.accessibilityPanel.querySelector('#reduced-motion');
            const fontSizeSelect = this.accessibilityPanel.querySelector('#font-size');
            const resetButton = this.accessibilityPanel.querySelector('.reset-settings');

            highContrastToggle.addEventListener('change', (e) => {
                this.preferences.highContrast = e.target.checked;
                this.applyTheme();
                this.savePreferences();
            });

            reducedMotionToggle.addEventListener('change', (e) => {
                this.preferences.reducedMotion = e.target.checked;
                this.applyTheme();
                this.savePreferences();
            });

            fontSizeSelect.addEventListener('change', (e) => {
                this.preferences.fontSize = e.target.value;
                this.applyTheme();
                this.savePreferences();
            });

            resetButton.addEventListener('click', () => {
                this.resetToDefaults();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + D for dark mode toggle
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.toggleTheme();
            }

            // Ctrl/Cmd + Shift + A for accessibility panel
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'A') {
                e.preventDefault();
                const menu = this.accessibilityPanel.querySelector('.accessibility-menu');
                menu.classList.toggle('open');
            }
        });
    }

    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme();
        this.savePreferences();
        
        // Update toggle button
        if (this.themeToggle) {
            this.themeToggle.innerHTML = this.getThemeIcon();
        }
    }

    applyTheme() {
        const root = document.documentElement;
        
        // Apply theme
        root.setAttribute('data-theme', this.currentTheme);
        
        // Apply accessibility preferences
        root.setAttribute('data-high-contrast', this.preferences.highContrast);
        root.setAttribute('data-reduced-motion', this.preferences.reducedMotion);
        root.setAttribute('data-font-size', this.preferences.fontSize);

        // Update meta theme color
        const themeColorMeta = document.querySelector('meta[name="theme-color"]');
        if (themeColorMeta) {
            themeColorMeta.content = this.currentTheme === 'dark' ? '#1d1d1f' : '#007aff';
        }

        // Announce theme change to screen readers
        this.announceThemeChange();
    }

    announceThemeChange() {
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        announcement.textContent = `已切换到${this.currentTheme === 'dark' ? '深色' : '浅色'}主题`;
        
        document.body.appendChild(announcement);
        
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    getThemeIcon() {
        return this.currentTheme === 'light' 
            ? `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                 <path d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36-.98 1.37-2.58 2.26-4.4 2.26-2.98 0-5.4-2.42-5.4-5.4 0-1.81.89-3.42 2.26-4.4-.44-.06-.9-.1-1.36-.1z"/>
               </svg>`
            : `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                 <path d="M12 7c-2.76 0-5 2.24-5 5s2.24 5 5 5 5-2.24 5-5-2.24-5-5-5zM2 13h2c.55 0 1-.45 1-1s-.45-1-1-1H2c-.55 0-1 .45-1 1s.45 1 1 1zm18 0h2c.55 0 1-.45 1-1s-.45-1-1-1h-2c-.55 0-1 .45-1 1s.45 1 1 1zM11 2v2c0 .55.45 1 1 1s1-.45 1-1V2c0-.55-.45-1-1-1s-1 .45-1 1zm0 18v2c0 .55.45 1 1 1s1-.45 1-1v-2c0-.55-.45-1-1-1s-1 .45-1 1zM5.99 4.58c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0s.39-1.03 0-1.41L5.99 4.58zm12.37 12.37c-.39-.39-1.03-.39-1.41 0-.39.39-.39 1.03 0 1.41l1.06 1.06c.39.39 1.03.39 1.41 0 .39-.39.39-1.03 0-1.41l-1.06-1.06zm1.06-10.96c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06zM7.05 18.36c.39-.39.39-1.03 0-1.41-.39-.39-1.03-.39-1.41 0l-1.06 1.06c-.39.39-.39 1.03 0 1.41s1.03.39 1.41 0l1.06-1.06z"/>
               </svg>`;
    }

    savePreferences() {
        try {
            const preferences = {
                theme: this.currentTheme,
                preferences: this.preferences
            };
            localStorage.setItem('theme-preferences', JSON.stringify(preferences));
        } catch (error) {
            console.warn('Failed to save theme preferences:', error);
        }
    }

    resetToDefaults() {
        this.currentTheme = 'light';
        this.preferences = {
            reducedMotion: false,
            highContrast: false,
            fontSize: 'normal'
        };
        
        this.applyTheme();
        this.savePreferences();
        
        // Update controls
        if (this.themeToggle) {
            this.themeToggle.innerHTML = this.getThemeIcon();
        }
        
        // Update accessibility panel
        const highContrastToggle = this.accessibilityPanel.querySelector('#high-contrast');
        const reducedMotionToggle = this.accessibilityPanel.querySelector('#reduced-motion');
        const fontSizeSelect = this.accessibilityPanel.querySelector('#font-size');
        
        highContrastToggle.checked = false;
        reducedMotionToggle.checked = false;
        fontSizeSelect.value = 'normal';
        
        this.showNotification('设置已重置为默认值', 'success');
    }

    showNotification(message, type = 'info') {
        // Use existing notification system if available
        if (window.subPagesUtils && window.subPagesUtils.showNotification) {
            window.subPagesUtils.showNotification(message, type);
        } else {
            console.log(`Theme System: ${message}`);
        }
    }

    // Public API
    getTheme() {
        return this.currentTheme;
    }

    setTheme(theme) {
        if (['light', 'dark'].includes(theme)) {
            this.currentTheme = theme;
            this.applyTheme();
            this.savePreferences();
        }
    }

    getPreferences() {
        return { ...this.preferences };
    }
}

// Initialize theme system
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.themeSystem = new ThemeSystem();
        console.log('Advanced theme system loaded');
    } catch (error) {
        console.error('Failed to initialize theme system:', error);
    }
});

// Export for debugging
window.ThemeSystem = ThemeSystem;
