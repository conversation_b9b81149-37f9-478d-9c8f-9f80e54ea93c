/**
 * Advanced Analytics and Monitoring System
 * User behavior tracking, performance analytics, and insights
 */

class AnalyticsSystem {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.userId = this.getUserId();
        this.events = [];
        this.pageViews = [];
        this.userInteractions = [];
        this.performanceMetrics = {};
        this.heatmapData = [];
        
        this.init();
    }

    init() {
        this.trackPageView();
        this.setupEventTracking();
        this.setupPerformanceTracking();
        this.setupHeatmapTracking();
        this.setupScrollTracking();
        this.setupFormTracking();
        this.startSessionTracking();
        
        console.log('Analytics system initialized');
        console.log('Session ID:', this.sessionId);
    }

    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getUserId() {
        let userId = localStorage.getItem('user_id');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('user_id', userId);
        }
        return userId;
    }

    trackPageView() {
        const pageView = {
            timestamp: Date.now(),
            url: window.location.href,
            pathname: window.location.pathname,
            referrer: document.referrer,
            userAgent: navigator.userAgent,
            screenResolution: `${screen.width}x${screen.height}`,
            viewportSize: `${window.innerWidth}x${window.innerHeight}`,
            sessionId: this.sessionId,
            userId: this.userId
        };

        this.pageViews.push(pageView);
        this.sendEvent('page_view', pageView);
    }

    setupEventTracking() {
        // Track clicks
        document.addEventListener('click', (e) => {
            this.trackClick(e);
        });

        // Track form submissions
        document.addEventListener('submit', (e) => {
            this.trackFormSubmission(e);
        });

        // Track downloads
        document.addEventListener('click', (e) => {
            if (e.target.closest('.download-item, [href$=".pdf"], [href$=".zip"]')) {
                this.trackDownload(e);
            }
        });

        // Track search
        document.addEventListener('input', (e) => {
            if (e.target.matches('input[type="search"], input[placeholder*="搜索"]')) {
                this.debounce(() => this.trackSearch(e), 500)();
            }
        });

        // Track theme changes
        document.addEventListener('themeChanged', (e) => {
            this.trackThemeChange(e.detail);
        });
    }

    trackClick(e) {
        const element = e.target;
        const clickData = {
            timestamp: Date.now(),
            elementType: element.tagName.toLowerCase(),
            elementId: element.id,
            elementClass: element.className,
            elementText: element.textContent?.substring(0, 100),
            href: element.href,
            position: { x: e.clientX, y: e.clientY },
            pageX: e.pageX,
            pageY: e.pageY,
            sessionId: this.sessionId
        };

        // Determine click type
        if (element.matches('button, .btn')) {
            clickData.type = 'button';
        } else if (element.matches('a')) {
            clickData.type = 'link';
        } else if (element.matches('.product-card, .service-card')) {
            clickData.type = 'card';
        } else {
            clickData.type = 'general';
        }

        this.userInteractions.push(clickData);
        this.sendEvent('click', clickData);

        // Track heatmap data
        this.heatmapData.push({
            x: e.pageX,
            y: e.pageY,
            timestamp: Date.now()
        });
    }

    trackFormSubmission(e) {
        const form = e.target;
        const formData = new FormData(form);
        const fields = {};
        
        for (const [key, value] of formData.entries()) {
            // Don't track sensitive data
            if (!this.isSensitiveField(key)) {
                fields[key] = typeof value === 'string' ? value.length : 'file';
            }
        }

        const submissionData = {
            timestamp: Date.now(),
            formId: form.id,
            formClass: form.className,
            action: form.action,
            method: form.method,
            fieldCount: Object.keys(fields).length,
            fields: fields,
            sessionId: this.sessionId
        };

        this.sendEvent('form_submission', submissionData);
    }

    trackDownload(e) {
        const link = e.target.closest('a, .download-item');
        const downloadData = {
            timestamp: Date.now(),
            fileName: this.extractFileName(link),
            fileType: this.extractFileType(link),
            source: 'download_button',
            sessionId: this.sessionId
        };

        this.sendEvent('download', downloadData);
    }

    trackSearch(e) {
        const query = e.target.value.trim();
        if (query.length < 2) return;

        const searchData = {
            timestamp: Date.now(),
            query: query,
            queryLength: query.length,
            source: 'search_input',
            sessionId: this.sessionId
        };

        this.sendEvent('search', searchData);
    }

    trackThemeChange(themeData) {
        const changeData = {
            timestamp: Date.now(),
            theme: themeData.theme,
            preferences: themeData.preferences,
            sessionId: this.sessionId
        };

        this.sendEvent('theme_change', changeData);
    }

    setupPerformanceTracking() {
        // Track Core Web Vitals
        this.trackCoreWebVitals();
        
        // Track custom performance metrics
        this.trackCustomMetrics();
        
        // Track resource loading
        this.trackResourceLoading();
    }

    trackCoreWebVitals() {
        // Largest Contentful Paint (LCP)
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            
            this.performanceMetrics.lcp = lastEntry.startTime;
            this.sendEvent('performance', {
                metric: 'lcp',
                value: lastEntry.startTime,
                timestamp: Date.now()
            });
        }).observe({ entryTypes: ['largest-contentful-paint'] });

        // First Input Delay (FID)
        new PerformanceObserver((entryList) => {
            const firstInput = entryList.getEntries()[0];
            
            this.performanceMetrics.fid = firstInput.processingStart - firstInput.startTime;
            this.sendEvent('performance', {
                metric: 'fid',
                value: firstInput.processingStart - firstInput.startTime,
                timestamp: Date.now()
            });
        }).observe({ entryTypes: ['first-input'] });

        // Cumulative Layout Shift (CLS)
        let clsValue = 0;
        new PerformanceObserver((entryList) => {
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    clsValue += entry.value;
                }
            }
            
            this.performanceMetrics.cls = clsValue;
            this.sendEvent('performance', {
                metric: 'cls',
                value: clsValue,
                timestamp: Date.now()
            });
        }).observe({ entryTypes: ['layout-shift'] });
    }

    trackCustomMetrics() {
        // Time to Interactive
        window.addEventListener('load', () => {
            setTimeout(() => {
                const tti = performance.now();
                this.performanceMetrics.tti = tti;
                this.sendEvent('performance', {
                    metric: 'tti',
                    value: tti,
                    timestamp: Date.now()
                });
            }, 0);
        });

        // JavaScript errors
        window.addEventListener('error', (e) => {
            this.sendEvent('javascript_error', {
                message: e.message,
                filename: e.filename,
                lineno: e.lineno,
                colno: e.colno,
                stack: e.error?.stack,
                timestamp: Date.now()
            });
        });

        // Promise rejections
        window.addEventListener('unhandledrejection', (e) => {
            this.sendEvent('promise_rejection', {
                reason: e.reason?.toString(),
                timestamp: Date.now()
            });
        });
    }

    trackResourceLoading() {
        window.addEventListener('load', () => {
            const resources = performance.getEntriesByType('resource');
            
            resources.forEach(resource => {
                if (resource.duration > 1000) { // Track slow resources
                    this.sendEvent('slow_resource', {
                        name: resource.name,
                        duration: resource.duration,
                        size: resource.transferSize,
                        type: resource.initiatorType,
                        timestamp: Date.now()
                    });
                }
            });
        });
    }

    setupHeatmapTracking() {
        // Track mouse movements (sampled)
        let mouseMoveCount = 0;
        document.addEventListener('mousemove', (e) => {
            mouseMoveCount++;
            if (mouseMoveCount % 10 === 0) { // Sample every 10th movement
                this.heatmapData.push({
                    x: e.pageX,
                    y: e.pageY,
                    type: 'move',
                    timestamp: Date.now()
                });
            }
        });

        // Track scroll heatmap
        this.setupScrollTracking();
    }

    setupScrollTracking() {
        let maxScroll = 0;
        const scrollMilestones = [25, 50, 75, 100];
        const reachedMilestones = new Set();

        window.addEventListener('scroll', this.throttle(() => {
            const scrollPercent = Math.round(
                (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
            );
            
            maxScroll = Math.max(maxScroll, scrollPercent);

            // Track scroll milestones
            scrollMilestones.forEach(milestone => {
                if (scrollPercent >= milestone && !reachedMilestones.has(milestone)) {
                    reachedMilestones.add(milestone);
                    this.sendEvent('scroll_milestone', {
                        milestone: milestone,
                        timestamp: Date.now(),
                        sessionId: this.sessionId
                    });
                }
            });
        }, 100));

        // Track max scroll on page unload
        window.addEventListener('beforeunload', () => {
            this.sendEvent('max_scroll', {
                maxScroll: maxScroll,
                timestamp: Date.now(),
                sessionId: this.sessionId
            });
        });
    }

    setupFormTracking() {
        // Track form field interactions
        document.addEventListener('focus', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.sendEvent('form_field_focus', {
                    fieldName: e.target.name,
                    fieldType: e.target.type,
                    timestamp: Date.now()
                });
            }
        });

        // Track form abandonment
        const formFields = new Map();
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                formFields.set(e.target.name, true);
            }
        });

        window.addEventListener('beforeunload', () => {
            if (formFields.size > 0) {
                this.sendEvent('form_abandonment', {
                    fieldsInteracted: Array.from(formFields.keys()),
                    timestamp: Date.now()
                });
            }
        });
    }

    startSessionTracking() {
        const sessionStart = Date.now();
        let lastActivity = sessionStart;

        // Track session duration
        const updateActivity = () => {
            lastActivity = Date.now();
        };

        ['click', 'scroll', 'keypress', 'mousemove'].forEach(event => {
            document.addEventListener(event, this.throttle(updateActivity, 1000));
        });

        // Send session data periodically
        setInterval(() => {
            const now = Date.now();
            const sessionDuration = now - sessionStart;
            const timeSinceLastActivity = now - lastActivity;

            if (timeSinceLastActivity < 30000) { // Active within last 30 seconds
                this.sendEvent('session_heartbeat', {
                    sessionDuration: sessionDuration,
                    timestamp: now,
                    sessionId: this.sessionId
                });
            }
        }, 30000);

        // Track session end
        window.addEventListener('beforeunload', () => {
            this.sendEvent('session_end', {
                sessionDuration: Date.now() - sessionStart,
                timestamp: Date.now(),
                sessionId: this.sessionId
            });
        });
    }

    sendEvent(eventType, eventData) {
        const event = {
            type: eventType,
            data: eventData,
            url: window.location.href,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            userId: this.userId
        };

        this.events.push(event);

        // In a real application, you would send this to your analytics server
        console.log('Analytics Event:', event);

        // Store in localStorage for debugging (limit to last 100 events)
        this.storeEventLocally(event);
    }

    storeEventLocally(event) {
        try {
            let storedEvents = JSON.parse(localStorage.getItem('analytics_events') || '[]');
            storedEvents.push(event);
            
            // Keep only last 100 events
            if (storedEvents.length > 100) {
                storedEvents = storedEvents.slice(-100);
            }
            
            localStorage.setItem('analytics_events', JSON.stringify(storedEvents));
        } catch (error) {
            console.warn('Failed to store analytics event:', error);
        }
    }

    // Utility methods
    isSensitiveField(fieldName) {
        const sensitiveFields = ['password', 'credit', 'card', 'ssn', 'social'];
        return sensitiveFields.some(field => 
            fieldName.toLowerCase().includes(field)
        );
    }

    extractFileName(element) {
        const href = element.href || element.querySelector('a')?.href;
        if (!href) return 'unknown';
        
        const url = new URL(href);
        return url.pathname.split('/').pop() || 'unknown';
    }

    extractFileType(element) {
        const fileName = this.extractFileName(element);
        const extension = fileName.split('.').pop();
        return extension || 'unknown';
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Public API
    getSessionData() {
        return {
            sessionId: this.sessionId,
            userId: this.userId,
            events: this.events,
            pageViews: this.pageViews,
            performanceMetrics: this.performanceMetrics,
            heatmapData: this.heatmapData
        };
    }

    getStoredEvents() {
        try {
            return JSON.parse(localStorage.getItem('analytics_events') || '[]');
        } catch {
            return [];
        }
    }

    clearStoredData() {
        localStorage.removeItem('analytics_events');
        this.events = [];
        this.pageViews = [];
        this.userInteractions = [];
        this.heatmapData = [];
    }

    trackCustomEvent(eventName, eventData) {
        this.sendEvent(eventName, eventData);
    }
}

// Initialize analytics system
document.addEventListener('DOMContentLoaded', () => {
    try {
        window.analyticsSystem = new AnalyticsSystem();
        console.log('Analytics system loaded');
    } catch (error) {
        console.error('Failed to initialize analytics system:', error);
    }
});

// Export for debugging
window.AnalyticsSystem = AnalyticsSystem;
